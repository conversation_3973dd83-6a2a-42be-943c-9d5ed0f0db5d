import { map, pick } from 'lodash';
import React, { ReactChild, useCallback, useMemo, forwardRef } from 'react';
import { useMutation } from 'react-apollo';
import classNames from 'classnames';

import ThreadMessageFormBody from './ThreadMessageFormBody';
import EntityForm, {
  IBasicEntity,
} from '../../../../../common/components/containers/EntityForm/EntityForm';
import getGqlOperationName from '../../../../../common/utils/getGqlOperationName';
import { IFileAttachmentTemp } from '../../../../../common/abstract/IFileAttachments';
import IThreadMessageItem from './abstract/IThreadMessageItem';
import useThreadMessageAction from './hook/useThreadMessageAction';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';
import useThreadMessagesList from './hook/useThreadMessagesList';
import { TextEditorRef } from '../../../../../common/components/controls/base/TextEditor/TextEditor';

import style from './ThreadMessageForm.scss';
import { dummyMutation } from '../../../../../common/data/dummyMutation';
import useIsConversationOpen from '../../hooks/useIsConversationOpen';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';

interface IThreadMessageFrom {
  parentId?: number | null;
  children?: ReactChild;
  isNotes?: boolean;
  showEditorBefore?: boolean;
  isEditMode?: boolean;
  setIsEditMode?: React.Dispatch<React.SetStateAction<boolean>>;
  editingMessageId?: number | null;
  setEditingMessageId?: React.Dispatch<React.SetStateAction<number | null>>;
  editingMessage?: IThreadMessageItem;
}

const ThreadMessageForm = forwardRef<TextEditorRef, IThreadMessageFrom>(
  (
    {
      children,
      parentId,
      isNotes,
      showEditorBefore,
      isEditMode,
      setIsEditMode,
      editingMessageId,
      setEditingMessageId,
      editingMessage,
    },
    ref,
  ) => {
    const { createQuery, updateQuery } = useThreadMessageAction();
    const [create] = useMutation(createQuery || dummyMutation);
    const [update] = useMutation(updateQuery || dummyMutation);
    const { selectedItem } = useEdComSelectedItem<IConversation>();
    const conversationId = selectedItem?.id as number;

    const { replaceItem, setItems, parentContext } = useThreadMessagesList();

    const entity = useMemo<IThreadMessageForm>(
      () => editingMessage || { content: '', attachments: [] },
      [editingMessage],
    );

    const {
      me: { id: currentUserId },
    } = useCurrentUser();

    const handleSubmit = useCallback(
      async (
        { id, attachments, content = '', threadQuote }: IThreadMessageForm,
        { resetForm },
      ) => {
        const params = {
          parentId,
          content,
          conversationId,
          attachments: map(attachments, a =>
            pick(a, ['description', 'fileName', 'fileId', 'uploadToken']),
          ),
          ...(threadQuote
            ? {
                quotedItemId: threadQuote.id,
                quotedItemContent: threadQuote.content,
              }
            : {}),
        };

        if (!parentId) {
          delete params.parentId;
        }

        if (id) {
          const result = await update({
            variables: {
              params,
              id,
            },
          });

          const item = result.data[getGqlOperationName(updateQuery)];
          {
            replaceItem(item);
            resetForm({ values: {} });
          }
          return result;
        }

        const result = await create({ variables: { params } });
        const item = result.data[getGqlOperationName(createQuery)];

        const parentItem =
          item.parentId && parentContext?.getItem(item.parentId);
        if (parentItem) {
          parentContext?.replaceItem({
            ...parentItem,
            threadSize: parentItem.threadSize + 1,
          });
        }
        setItems((items: IThreadMessageItem[]) => [...items, item]);
        resetForm({ values: {} });

        return result;
      },
      [
        create,
        update,
        conversationId,
        createQuery,
        updateQuery,
        replaceItem,
        parentId,
        setItems,
        parentContext,
      ],
    );

    const isConversationOpen = useIsConversationOpen();

    const submitSectionRenderer = useCallback(() => <></>, []);

    if (
      isConversationOpen &&
      (!isNotes || selectedItem?.ownerPersonId === currentUserId)
    ) {
      return (
        <div className={classNames(style.main, { [style.reply]: parentId })}>
          <EntityForm
            createLabel="Send"
            entity={entity}
            submitSectionRenderer={submitSectionRenderer}
            successMessageText="Sent Successfully"
            updateLabel="Send"
            onSubmit={handleSubmit}
          >
            {showEditorBefore ? (
              <>
                <ThreadMessageFormBody
                  ref={ref}
                  editingMessageId={editingMessageId}
                  isEditMode={isEditMode}
                  parentId={parentId}
                  setEditingMessageId={setEditingMessageId}
                  setIsEditMode={setIsEditMode}
                />
                <div className="threadList mt-15">{children}</div>
              </>
            ) : (
              <>
                <div className="threadList">{children}</div>
                <ThreadMessageFormBody
                  ref={ref}
                  editingMessageId={editingMessageId}
                  isEditMode={isEditMode}
                  parentId={parentId}
                  setEditingMessageId={setEditingMessageId}
                  setIsEditMode={setIsEditMode}
                />
              </>
            )}
          </EntityForm>
        </div>
      );
    }

    return (
      <div className={classNames(style.main, { [style.reply]: parentId })}>
        <div className="threadList">{children}</div>
      </div>
    );
  },
);

ThreadMessageForm.displayName = 'ThreadMessageForm';

export default ThreadMessageForm;

interface IThreadMessageForm extends IBasicEntity {
  content: string;
  attachments: IFileAttachmentTemp[];
  threadQuote?: IThreadMessageItem;
}
